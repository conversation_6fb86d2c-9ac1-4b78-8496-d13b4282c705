import { z } from 'zod';
import { createSchemaPair } from '@/schemas/schemaUtils';
import {
  DraftExerciseStatusEnum,
  ExerciseTypeEnum,
  DifficultyEnum,
  DraftMediaTypeEnum,

} from '@/types/internal/enums';
import { EditorUserSchema } from './authSchema';
import {
  exerciseDataSchemaMap,
  validateExerciseData,
  validateExerciseSolution,
  ExerciseSolutionSchema
} from './exerciseDataValidation';

// Learning Node Reference Schema
const LearningNodeReferenceSchemaBase = z.object({
  id: z.number(),
  title: z.string(),
  publicId: z.string(),
  chapter: z.object({
    id: z.number(),
    title: z.string(),
    publicId: z.string(),
    subject: z.object({
      id: z.number(),
      name: z.string(),
      publicId: z.string(),
    }),
  }),
});

// Draft Media File Schema
const DraftMediaFileSchemaBase = z.object({
  id: z.number(),
  publicId: z.string(),
  mediaType: DraftMediaTypeEnum,
  url: z.string().url(),
  storagePath: z.string(),
  originalFilename: z.string(),
  contentType: z.string(),
  fileSize: z.number(),
  metadata: z.object({
    width: z.number().optional(),
    height: z.number().optional(),
    duration: z.number().optional(),
  }).optional(),
  createdAt: z.string(),
});



// Assigned Editor Schema
const AssignedEditorSchemaBase = z.object({
  id: z.number(),
  email: z.string().email(),
  name: z.string().optional(),
});

// Draft Exercise Schemas
const DraftExerciseSchemaBase = z.object({
  id: z.number(),
  publicId: z.string(),
  exerciseType: ExerciseTypeEnum,
  difficulty: DifficultyEnum,
  dataJson: z.record(z.any()), // Will be validated based on exerciseType in practice
  solutionJson: z.record(z.any()), // Will be validated against ExerciseSolutionSchema in practice
  status: DraftExerciseStatusEnum,
  assignedEditorId: z.number().nullable(),
  assignedEditor: AssignedEditorSchemaBase.optional(),
  rejectReason: z.string().nullable(),
  publishedExerciseId: z.number().nullable(),
  sourceExerciseId: z.number().nullable().optional(),
  learningNode: LearningNodeReferenceSchemaBase,
  mediaFiles: z.array(DraftMediaFileSchemaBase),

  createdAt: z.string(),
  updatedAt: z.string(),
  acceptedAt: z.string().optional(),
  publishedAt: z.string().optional(),
});

const DraftExerciseApiSchemaBase = z.object({
  id: z.number(),
  public_id: z.string(),
  exercise_type: ExerciseTypeEnum,
  difficulty: DifficultyEnum,
  data_json: z.record(z.any()),
  solution_json: z.record(z.any()),
  status: DraftExerciseStatusEnum,
  assigned_editor_id: z.number().nullable(),
  assigned_editor: z.object({
    id: z.number(),
    email: z.string().email(),
    name: z.string().optional(),
  }).optional(),
  reject_reason: z.string().nullable(),
  published_exercise_id: z.number().nullable(),
  source_exercise_id: z.number().nullable().optional(),
  learning_node: z.object({
    id: z.number(),
    title: z.string(),
    public_id: z.string(),
    chapter: z.object({
      id: z.number(),
      title: z.string(),
      public_id: z.string(),
      subject: z.object({
        id: z.number(),
        name: z.string(),
        public_id: z.string(),
      }),
    }),
  }),
  media_files: z.array(z.object({
    id: z.number(),
    public_id: z.string(),
    media_type: DraftMediaTypeEnum,
    url: z.string().url(),
    storage_path: z.string(),
    original_filename: z.string(),
    content_type: z.string(),
    file_size: z.number(),
    metadata: z.object({
      width: z.number().optional(),
      height: z.number().optional(),
      duration: z.number().optional(),
    }).optional(),
    created_at: z.string(),
  })),

  created_at: z.string(),
  updated_at: z.string(),
  accepted_at: z.string().optional(),
  published_at: z.string().optional(),
});

// Get Drafts Request Schemas
const GetDraftsRequestAppSchema = z.object({
  status: DraftExerciseStatusEnum.optional(),
  subjectId: z.number().optional(),
  chapterId: z.number().optional(),
  learningNodeId: z.number().optional(),
  editorId: z.number().optional(),
  exerciseType: ExerciseTypeEnum.optional(),
  difficulty: DifficultyEnum.optional(),
  dateFrom: z.string().optional(),
  dateTo: z.string().optional(),
  page: z.number().min(1).default(1),
  pageSize: z.number().min(1).max(100).default(50),
});

const GetDraftsRequestApiSchema = z.object({
  status: DraftExerciseStatusEnum.optional(),
  subject_id: z.number().optional(),
  chapter_id: z.number().optional(),
  learning_node_id: z.number().optional(),
  editor_id: z.number().optional(),
  exercise_type: ExerciseTypeEnum.optional(),
  difficulty: DifficultyEnum.optional(),
  date_from: z.string().optional(),
  date_to: z.string().optional(),
  page: z.number().min(1).default(1),
  page_size: z.number().min(1).max(100).default(50),
});

export const GetDraftsRequestSchema = createSchemaPair(
  GetDraftsRequestAppSchema,
  GetDraftsRequestApiSchema
);

// Draft List Item Schema - Matches backend DraftListItemResponse exactly
const DraftListItemSchemaBase = z.object({
  id: z.number(),
  publicId: z.string(),
  title: z.string(), // Backend extracts title from data_json
  subject: z.object({
    name: z.string(),
    publicId: z.string(),
  }),
  chapter: z.object({
    title: z.string(),
    publicId: z.string().nullable(), // Backend can return null
  }),
  learningNode: z.object({
    title: z.string(),
    publicId: z.string(),
  }),
  status: DraftExerciseStatusEnum,
  updatedAt: z.string(),
  assignedEditor: z.string().nullable(), // Email string, can be null
  mediaCount: z.number(), // Backend returns media_count as number
});

const DraftListItemApiSchemaBase = z.object({
  id: z.number(),
  public_id: z.string(),
  title: z.string(), // Backend provides title extracted from data_json
  subject: z.object({
    name: z.string(),
    public_id: z.string(),
  }),
  chapter: z.object({
    title: z.string(),
    public_id: z.string().nullable(), // Backend can return null
  }),
  learning_node: z.object({
    title: z.string(),
    public_id: z.string(),
  }),
  status: DraftExerciseStatusEnum,
  updated_at: z.string(),
  assigned_editor: z.string().nullable(), // Email string, can be null
  media_count: z.number(), // Backend returns media_count as number
});

// Get Drafts Response Schemas
const GetDraftsResponseAppSchema = z.object({
  drafts: z.array(DraftListItemSchemaBase),
  pagination: z.object({
    page: z.number(),
    pageSize: z.number(),
    totalItems: z.number(),
    totalPages: z.number(),
  }).optional(),
});

const GetDraftsResponseApiSchema = z.object({
  drafts: z.array(DraftListItemApiSchemaBase),
  pagination: z.object({
    page: z.number(),
    page_size: z.number(),
    total_items: z.number(),
    total_pages: z.number(),
  }).optional(),
});

// Use standard createSchemaPair - customFetch handles automatic transformation
export const GetDraftsResponseSchema = createSchemaPair(
  GetDraftsResponseAppSchema,
  GetDraftsResponseApiSchema
);

// Get Draft Detail Request Schemas (just the ID in path)
const GetDraftDetailRequestAppSchema = z.object({}); // No request body
const GetDraftDetailRequestApiSchema = z.object({});

export const GetDraftDetailRequestSchema = createSchemaPair(
  GetDraftDetailRequestAppSchema,
  GetDraftDetailRequestApiSchema
);

// Simplified Draft Detail Schema (only essential fields for editing)
const SimplifiedDraftDetailSchemaBase = z.object({
  id: z.number(),
  publicId: z.string(),
  exerciseType: ExerciseTypeEnum,
  difficulty: DifficultyEnum.optional(),
  dataJson: z.record(z.any()),
  solutionJson: z.record(z.any()).optional(),
  status: DraftExerciseStatusEnum,
  rejectReason: z.string().nullable().optional(), // Only needed if draft was rejected
  createdAt: z.string(),
  updatedAt: z.string(),
});

const SimplifiedDraftDetailApiSchemaBase = z.object({
  id: z.number(),
  public_id: z.string(),
  exercise_type: ExerciseTypeEnum,
  difficulty: DifficultyEnum.optional(),
  data_json: z.record(z.any()),
  solution_json: z.record(z.any()).optional(),
  status: DraftExerciseStatusEnum,
  reject_reason: z.string().nullable().optional(), // Only needed if draft was rejected
  created_at: z.string(),
  updated_at: z.string(),
  // Handle backend fields that we don't need but might be present
  assigned_editor: z.any().optional(), // Backend returns object, we ignore it
  media: z.array(z.any()).optional(), // Backend returns media array, we ignore it

  learning_node: z.object({
    title: z.string(),
    public_id: z.string(),
  }).optional(), // Backend returns single learning_node
  published_exercise_id: z.number().nullable().optional(),
  assigned_editor_id: z.number().nullable().optional(),
});

// Get Draft Detail Response Schemas
const GetDraftDetailResponseAppSchema = SimplifiedDraftDetailSchemaBase;
const GetDraftDetailResponseApiSchema = SimplifiedDraftDetailApiSchemaBase;

export const GetDraftDetailResponseSchema = createSchemaPair(
  GetDraftDetailResponseAppSchema,
  GetDraftDetailResponseApiSchema
);

// Update Draft Request Schemas
const UpdateDraftRequestAppSchema = z.object({
  dataJson: z.record(z.any()).optional(),
  solutionJson: z.record(z.any()).optional(),
  difficulty: DifficultyEnum.optional(),
});

const UpdateDraftRequestApiSchema = z.object({
  data_json: z.record(z.any()).optional(),
  solution_json: z.record(z.any()).optional(),
  difficulty: DifficultyEnum.optional(),
});

export const UpdateDraftRequestSchema = createSchemaPair(
  UpdateDraftRequestAppSchema,
  UpdateDraftRequestApiSchema
);

// Update Draft Response Schemas
const UpdateDraftResponseAppSchema = z.object({
  success: z.boolean(),
  message: z.string(),
  draft: SimplifiedDraftDetailSchemaBase,
});

const UpdateDraftResponseApiSchema = z.object({
  success: z.boolean(),
  message: z.string(),
  draft: SimplifiedDraftDetailApiSchemaBase,
});

export const UpdateDraftResponseSchema = createSchemaPair(
  UpdateDraftResponseAppSchema,
  UpdateDraftResponseApiSchema
);

// Accept Draft Request Schemas
const AcceptDraftRequestAppSchema = z.object({
});

const AcceptDraftRequestApiSchema = z.object({
});

export const AcceptDraftRequestSchema = createSchemaPair(
  AcceptDraftRequestAppSchema,
  AcceptDraftRequestApiSchema
);

// Accept Draft Response Schemas
const AcceptDraftResponseAppSchema = z.object({
  success: z.boolean(),
  message: z.string(),
  draft: z.object({
    id: z.number(),
    status: z.literal('ACCEPTED_BY_EDITOR'),
  }),
});

const AcceptDraftResponseApiSchema = z.object({
  success: z.boolean(),
  message: z.string(),
  draft: z.object({
    id: z.number(),
    status: z.literal('ACCEPTED_BY_EDITOR'),
  }),
});

export const AcceptDraftResponseSchema = createSchemaPair(
  AcceptDraftResponseAppSchema,
  AcceptDraftResponseApiSchema
);

// Create Draft Request Schemas
const CreateDraftRequestAppSchema = z.object({
  exerciseType: ExerciseTypeEnum,
  difficulty: DifficultyEnum,
  dataJson: z.record(z.any()),
  solutionJson: z.record(z.any()),
  learningNodeIds: z.array(z.string()),
});

const CreateDraftRequestApiSchema = z.object({
  exercise_type: ExerciseTypeEnum,
  difficulty: DifficultyEnum,
  data_json: z.record(z.any()),
  solution_json: z.record(z.any()),
  learning_node_ids: z.array(z.string()),
});

export const CreateDraftRequestSchema = createSchemaPair(
  CreateDraftRequestAppSchema,
  CreateDraftRequestApiSchema
);

// Minimal Draft Response Schema for Creation (only essential fields)
const MinimalDraftSchemaBase = z.object({
  id: z.number(),
  publicId: z.string(),
  status: DraftExerciseStatusEnum,
  exerciseType: ExerciseTypeEnum,
  difficulty: DifficultyEnum.optional(),
  dataJson: z.record(z.any()),
  solutionJson: z.record(z.any()).optional(),
  createdAt: z.string(),
  updatedAt: z.string(),
});

const MinimalDraftApiSchemaBase = z.object({
  id: z.number(),
  public_id: z.string(),
  status: DraftExerciseStatusEnum,
  exercise_type: ExerciseTypeEnum,
  difficulty: DifficultyEnum.optional(),
  data_json: z.record(z.any()),
  solution_json: z.record(z.any()).optional(),
  created_at: z.string(),
  updated_at: z.string(),
});

// Create Draft Response Schemas
const CreateDraftResponseAppSchema = z.object({
  success: z.boolean(),
  message: z.string(),
  draft: MinimalDraftSchemaBase,
});

const CreateDraftResponseApiSchema = z.object({
  success: z.boolean(),
  message: z.string(),
  draft: MinimalDraftApiSchemaBase,
});

// Use standard createSchemaPair - customFetch handles automatic transformation
export const CreateDraftResponseSchema = createSchemaPair(
  CreateDraftResponseAppSchema,
  CreateDraftResponseApiSchema
);

// Export types
export type GetDraftsRequestApp = z.infer<typeof GetDraftsRequestSchema.frontend>;
export type GetDraftsResponseApp = z.infer<typeof GetDraftsResponseSchema.frontend>;
export type DraftListItemApp = z.infer<typeof DraftListItemSchemaBase>;
export type GetDraftDetailResponseApp = z.infer<typeof GetDraftDetailResponseSchema.frontend>;
export type UpdateDraftRequestApp = z.infer<typeof UpdateDraftRequestSchema.frontend>;
export type UpdateDraftResponseApp = z.infer<typeof UpdateDraftResponseSchema.frontend>;
export type AcceptDraftRequestApp = z.infer<typeof AcceptDraftRequestSchema.frontend>;
export type AcceptDraftResponseApp = z.infer<typeof AcceptDraftResponseSchema.frontend>;
export type CreateDraftRequestApp = z.infer<typeof CreateDraftRequestSchema.frontend>;
export type CreateDraftResponseApp = z.infer<typeof CreateDraftResponseSchema.frontend>;

// Utility functions for validating draft data
export function validateDraftData(exerciseType: z.infer<typeof ExerciseTypeEnum>, dataJson: any, solutionJson?: any) {
  // Validate exercise data
  const validatedData = validateExerciseData(exerciseType as keyof typeof exerciseDataSchemaMap, dataJson);

  // Validate solution if provided
  let validatedSolution;
  if (solutionJson) {
    validatedSolution = validateExerciseSolution(solutionJson);
  }

  return {
    dataJson: validatedData,
    solutionJson: validatedSolution,
  };
}

export function isDraftDataValid(exerciseType: z.infer<typeof ExerciseTypeEnum>, dataJson: any, solutionJson?: any): boolean {
  try {
    validateDraftData(exerciseType, dataJson, solutionJson);
    return true;
  } catch {
    return false;
  }
}