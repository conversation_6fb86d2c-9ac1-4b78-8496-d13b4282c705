import { create } from 'zustand';
import {
  getUsers,
  createUser,
  updateUser,
  updateUserByPublicId,
  getEditorByPublicId,
  addEditorScope,
  addEditorScopeByPublicId,
  removeEditorScope,
  removeEditorScopeByPublicId,
  deleteEditor as deleteEditorService,
  permanentlyDeleteEditor as permanentlyDeleteEditorService
} from '@/services/internal/adminService';
import { GetUsersResponseApp, CreateUserRequestApp, UpdateUserRequestApp, AddScopeRequestApp } from '@/schemas/internal/adminSchema';
import { EditorRole } from '@/types/internal/authTypes';
import { PAGINATION_CONFIG } from '@/config/constants';

interface EditorScope {
  id: number;
  editorId: number;
  scopeType: 'SUBJECT' | 'CHAPTER' | 'LEARNING_NODE';
  scopeValue: string;
  name?: string;
  createdAt: string;
}

interface Editor {
  id: number;
  publicId: string;
  email: string;
  role: EditorR<PERSON>;
  scopes: EditorScope[];
  isActive: boolean;
  createdAt: string;
  lastLoginAt?: string;
}

interface EditorsState {
  editors: Editor[];
  selectedEditor: Editor | null;
  filters: {
    role?: EditorRole;
    search?: string;
    isActive?: boolean;
  };
  pagination: {
    page: number;
    pageSize: number;
    total: number;
    totalPages: number;
  };
  isLoading: boolean;
  isCreating: boolean;
  isUpdating: boolean;
  isLoadingEditor: boolean;
  error: string | null;
  lastRefresh: string | null;
}

interface EditorsActions {
  // Fetch actions
  fetchEditors: () => Promise<void>;
  fetchEditorById: (editorId: number) => Promise<void>;
  fetchEditorByPublicId: (editorPublicId: string) => Promise<void>;

  // CRUD actions
  createEditor: (params: CreateUserRequestApp) => Promise<boolean>;
  updateEditor: (editorId: number, params: UpdateUserRequestApp) => Promise<boolean>;
  updateEditorByPublicId: (editorPublicId: string, params: UpdateUserRequestApp) => Promise<boolean>;
  deleteEditor: (editorPublicId: string) => Promise<boolean>;
  permanentlyDeleteEditor: (editorPublicId: string) => Promise<boolean>;
  toggleEditorStatus: (editorId: number) => Promise<boolean>;

  // Scope management
  addScope: (editorId: number, params: AddScopeRequestApp) => Promise<boolean>;
  addScopeByPublicId: (editorPublicId: string, params: AddScopeRequestApp) => Promise<boolean>;
  removeScope: (editorId: number, scopeId: number) => Promise<boolean>;
  removeScopeByPublicId: (editorPublicId: string, scopeId: number) => Promise<boolean>;
  
  // Filter and pagination
  setFilters: (filters: Partial<EditorsState['filters']>) => void;
  setPage: (page: number) => void;
  setPageSize: (pageSize: number) => void;
  clearFilters: () => void;
  
  // Utility actions
  setSelectedEditor: (editor: Editor | null) => void;
  refresh: () => Promise<void>;
  reset: () => void;
  
  // Internal setters
  _setEditors: (editors: Editor[]) => void;
  _setLoading: (isLoading: boolean) => void;
  _setError: (error: string | null) => void;
}

export type EditorsStore = EditorsState & EditorsActions;

const initialState: EditorsState = {
  editors: [],
  selectedEditor: null,
  filters: {},
  pagination: {
    page: 1,
    pageSize: 20,
    total: 0,
    totalPages: 0,
  },
  isLoading: false,
  isCreating: false,
  isUpdating: false,
  isLoadingEditor: false,
  error: null,
  lastRefresh: null,
};

export const useEditorsStore = create<EditorsStore>((set, get) => ({
  ...initialState,

  fetchEditors: async () => {
    set({ isLoading: true, error: null });

    try {
      const { filters, pagination } = get();
      
      const result = await getUsers({
        page: pagination.page,
        limit: pagination.pageSize,
        role: filters.role,
        search: filters.search,
        isActive: filters.isActive, // Use filter value, undefined means fetch all
      });

      if (result.status === 'success') {
        set({
          editors: result.data.editors, // Updated to match backend field name
          pagination: {
            ...pagination,
            total: result.data.total, // Updated to match backend field name
            totalPages: result.data.totalPages, // Already matches (total_pages -> totalPages)
          },
          isLoading: false,
          lastRefresh: new Date().toISOString(),
        });
      } else {
        throw new Error(result.message || 'Failed to fetch editors');
      }
    } catch (error) {
      console.error('Failed to fetch editors:', error);
      set({
        error: error instanceof Error ? error.message : 'Failed to fetch editors',
        isLoading: false,
      });
    }
  },

  fetchEditorById: async (editorId: number) => {
    set({ isLoadingEditor: true, error: null });

    try {
      // First check if we already have this editor in our list
      const { editors } = get();
      const existingEditor = editors.find(e => e.id === editorId);
      
      if (existingEditor) {
        set({
          selectedEditor: existingEditor,
          isLoadingEditor: false,
        });
        return;
      }

      // If not in list, fetch all editors and find the one we need
      // This is necessary because there's no individual GET endpoint
      const result = await getUsers({
        page: 1,
        limit: 100, // Fetch more to ensure we get the editor
        isActive: undefined, // Fetch all editors (active and inactive)
      });

      if (result.status === 'success') {
        const editor = result.data.users.find(e => e.id === editorId);
        
        if (editor) {
          set({
            selectedEditor: editor,
            editors: result.data.users,
            isLoadingEditor: false,
          });
        } else {
          throw new Error('Editor not found');
        }
      } else {
        throw new Error(result.message || 'Failed to fetch editor details');
      }
    } catch (error) {
      console.error('Failed to fetch editor:', error);
      set({
        error: error instanceof Error ? error.message : 'Failed to fetch editor details',
        isLoadingEditor: false,
      });
    }
  },

  fetchEditorByPublicId: async (editorPublicId: string) => {
    set({ isLoadingEditor: true, error: null });

    try {
      // First check if we already have this editor in our list
      const { editors } = get();
      const existingEditor = editors.find(e => e.publicId === editorPublicId);

      if (existingEditor) {
        set({
          selectedEditor: existingEditor,
          isLoadingEditor: false,
        });
        return;
      }

      // Try to fetch the specific editor by public ID
      const result = await getEditorByPublicId(editorPublicId);

      if (result.status === 'success') {
        set({
          selectedEditor: result.data,
          isLoadingEditor: false,
        });
      } else {
        throw new Error(result.message || 'Failed to fetch editor details');
      }
    } catch (error) {
      console.error('Failed to fetch editor:', error);
      set({
        error: error instanceof Error ? error.message : 'Failed to fetch editor details',
        isLoadingEditor: false,
      });
    }
  },

  createEditor: async (params: CreateUserRequestApp) => {
    set({ isCreating: true, error: null });

    try {
      const result = await createUser(params);

      if (result.status === 'success') {
        set({ isCreating: false });
        // Refresh the list to include the new editor
        await get().fetchEditors();
        return true;
      } else {
        throw new Error(result.message || 'Failed to create editor');
      }
    } catch (error) {
      console.error('Failed to create editor:', error);
      set({
        error: error instanceof Error ? error.message : 'Failed to create editor',
        isCreating: false,
      });
      return false;
    }
  },

  updateEditor: async (editorId: number, params: UpdateUserRequestApp) => {
    set({ isUpdating: true, error: null });

    try {
      const result = await updateUser(editorId, params);

      if (result.status === 'success') {
        // Update the editor in the list, filtering out unnecessary fields
        const updatedEditor: Editor = {
          id: result.data.id,
          publicId: result.data.publicId,
          email: result.data.email,
          role: result.data.role,
          scopes: result.data.scopes as EditorScope[],
          isActive: result.data.isActive,
          createdAt: result.data.createdAt,
          lastLoginAt: result.data.lastLoginAt || undefined,
        };
        
        set(state => ({
          editors: state.editors.map(editor =>
            editor.id === editorId ? updatedEditor : editor
          ),
          selectedEditor: state.selectedEditor?.id === editorId 
            ? updatedEditor
            : state.selectedEditor,
          isUpdating: false,
        }));
        return true;
      } else {
        throw new Error(result.message || 'Failed to update editor');
      }
    } catch (error) {
      console.error('Failed to update editor:', error);
      set({
        error: error instanceof Error ? error.message : 'Failed to update editor',
        isUpdating: false,
      });
      return false;
    }
  },

  updateEditorByPublicId: async (editorPublicId: string, params: UpdateUserRequestApp) => {
    set({ isUpdating: true, error: null });

    try {
      const result = await updateUserByPublicId(editorPublicId, params);

      if (result.status === 'success') {
        // Update the editor in the list and selected editor, filtering out unnecessary fields
        const updatedEditor: Editor = {
          id: result.data.id,
          publicId: result.data.publicId,
          email: result.data.email,
          role: result.data.role,
          scopes: result.data.scopes as EditorScope[],
          isActive: result.data.isActive,
          createdAt: result.data.createdAt,
          lastLoginAt: result.data.lastLoginAt || undefined,
        };
        
        set(state => ({
          editors: state.editors.map(editor =>
            editor.publicId === editorPublicId ? updatedEditor : editor
          ),
          selectedEditor: state.selectedEditor?.publicId === editorPublicId
            ? updatedEditor
            : state.selectedEditor,
          isUpdating: false,
        }));
        return true;
      } else {
        throw new Error(result.message || 'Failed to update editor');
      }
    } catch (error) {
      console.error('Failed to update editor by public ID:', error);
      set({
        error: error instanceof Error ? error.message : 'Failed to update editor',
        isUpdating: false,
      });
      return false;
    }
  },

  deleteEditor: async (editorPublicId: string) => {
    set({ isUpdating: true, error: null });

    try {
      const result = await deleteEditorService(editorPublicId);

      if (result.status === 'success') {
        // Update the editor to be inactive (soft delete)
        set(state => ({
          editors: state.editors.map(editor =>
            editor.publicId === editorPublicId
              ? { ...editor, isActive: false }
              : editor
          ),
          selectedEditor: state.selectedEditor?.publicId === editorPublicId
            ? { ...state.selectedEditor, isActive: false }
            : state.selectedEditor,
          isUpdating: false,
        }));
        return true;
      } else {
        throw new Error(result.message || 'Failed to delete editor');
      }
    } catch (error) {
      console.error('Failed to delete editor:', error);
      set({
        error: error instanceof Error ? error.message : 'Failed to delete editor',
        isUpdating: false,
      });
      return false;
    }
  },

  permanentlyDeleteEditor: async (editorPublicId: string) => {
    set({ isUpdating: true, error: null });

    try {
      const result = await permanentlyDeleteEditorService(editorPublicId);

      if (result.status === 'success') {
        // Remove the editor from the list
        set(state => ({
          editors: state.editors.filter(editor => editor.publicId !== editorPublicId),
          selectedEditor: state.selectedEditor?.publicId === editorPublicId
            ? null
            : state.selectedEditor,
          isUpdating: false,
        }));
        return true;
      } else {
        throw new Error(result.message || 'Failed to permanently delete editor');
      }
    } catch (error) {
      console.error('Failed to permanently delete editor:', error);
      set({
        error: error instanceof Error ? error.message : 'Failed to permanently delete editor',
        isUpdating: false,
      });
      return false;
    }
  },

  toggleEditorStatus: async (editorId: number) => {
    const editor = get().editors.find(e => e.id === editorId);
    if (!editor) return false;

    return get().updateEditorByPublicId(editor.publicId, {
      email: editor.email,
      isActive: !editor.isActive,
      role: editor.role
    });
  },

  addScope: async (editorId: number, params: AddScopeRequestApp) => {
    set({ isUpdating: true, error: null });

    try {
      const result = await addEditorScope(editorId, params);

      if (result.status === 'success' && result.data.success) {
        // Refresh the editor to get updated scopes
        // Since there's no individual GET endpoint, we need to refetch the list
        await get().fetchEditorById(editorId);
        set({ isUpdating: false });
        return true;
      } else {
        throw new Error(result.message || 'Failed to add scope');
      }
    } catch (error) {
      console.error('Failed to add scope:', error);
      set({
        error: error instanceof Error ? error.message : 'Failed to add scope',
        isUpdating: false,
      });
      return false;
    }
  },

  removeScope: async (editorId: number, scopeId: number) => {
    set({ isUpdating: true, error: null });

    try {
      const result = await removeEditorScope(editorId, scopeId);

      if (result.status === 'success') {
        // Refresh the editor to get updated scopes
        // Since there's no individual GET endpoint, we need to refetch the list
        await get().fetchEditorById(editorId);
        set({ isUpdating: false });
        return true;
      } else {
        throw new Error(result.message || 'Failed to remove scope');
      }
    } catch (error) {
      console.error('Failed to remove scope:', error);
      set({
        error: error instanceof Error ? error.message : 'Failed to remove scope',
        isUpdating: false,
      });
      return false;
    }
  },

  addScopeByPublicId: async (editorPublicId: string, params: AddScopeRequestApp) => {
    set({ isUpdating: true, error: null });

    try {
      const result = await addEditorScopeByPublicId(editorPublicId, params);

      if (result.status === 'success' && result.data.success) {
        // Refresh the editor to get updated scopes
        await get().fetchEditorByPublicId(editorPublicId);
        set({ isUpdating: false });
        return true;
      } else {
        throw new Error(result.message || 'Failed to add scope');
      }
    } catch (error) {
      console.error('Failed to add scope by public ID:', error);
      set({
        error: error instanceof Error ? error.message : 'Failed to add scope',
        isUpdating: false,
      });
      return false;
    }
  },

  removeScopeByPublicId: async (editorPublicId: string, scopeId: number) => {
    set({ isUpdating: true, error: null });

    try {
      const result = await removeEditorScopeByPublicId(editorPublicId, scopeId);

      if (result.status === 'success' && result.data.success) {
        // Refresh the editor to get updated scopes
        await get().fetchEditorByPublicId(editorPublicId);
        set({ isUpdating: false });
        return true;
      } else {
        throw new Error(result.message || 'Failed to remove scope');
      }
    } catch (error) {
      console.error('Failed to remove scope by public ID:', error);
      set({
        error: error instanceof Error ? error.message : 'Failed to remove scope',
        isUpdating: false,
      });
      return false;
    }
  },

  setFilters: (newFilters) => {
    set(state => ({
      filters: { ...state.filters, ...newFilters },
      pagination: { ...state.pagination, page: 1 }, // Reset to first page
    }));
    // Auto-fetch when filters change
    get().fetchEditors();
  },

  setPage: (page) => {
    set(state => ({
      pagination: { ...state.pagination, page }
    }));
    get().fetchEditors();
  },

  setPageSize: (pageSize) => {
    set(state => ({
      pagination: { ...state.pagination, pageSize, page: 1 }
    }));
    get().fetchEditors();
  },

  clearFilters: () => {
    set({ filters: {}, pagination: { ...get().pagination, page: 1 } });
    get().fetchEditors();
  },

  setSelectedEditor: (editor) => {
    set({ selectedEditor: editor });
  },

  refresh: () => {
    return get().fetchEditors();
  },

  reset: () => {
    set(initialState);
  },

  // Internal setters
  _setEditors: (editors) => set({ editors }),
  _setLoading: (isLoading) => set({ isLoading }),
  _setError: (error) => set({ error }),
}));

// Selector helpers
export const selectEditors = (state: EditorsStore) => state.editors;
export const selectSelectedEditor = (state: EditorsStore) => state.selectedEditor;
export const selectFilters = (state: EditorsStore) => state.filters;
export const selectPagination = (state: EditorsStore) => state.pagination;
export const selectIsLoading = (state: EditorsStore) => state.isLoading;
export const selectIsCreating = (state: EditorsStore) => state.isCreating;
export const selectIsUpdating = (state: EditorsStore) => state.isUpdating;
export const selectIsLoadingEditor = (state: EditorsStore) => state.isLoadingEditor;
export const selectError = (state: EditorsStore) => state.error;